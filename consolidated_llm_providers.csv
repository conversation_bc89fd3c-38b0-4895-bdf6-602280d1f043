Provider,Free_Tier_Available,Free_Credits_Details,Rate_Limits,Request_Limits,Usage_Limits,Key_Models_Available,Registration_Requirements,Data_Usage_Training,Limitations_Notes,Reference_URL,Source_Files
OpenAI,Yes (trial only),$5-$18 credit (one-time for new users),"20,000 TPM, 200 RPM",Limited by credits/payment method,No ongoing free tier,"GPT-3.5 Turbo, GPT-4, GPT-4o, GPT-4o mini",Account + Phone + Payment method,Not used for training (paid tier only),"Free tier discontinued, requires payment method for API access; GPT-3.5 is cheaper, GPT-4o is fastest & multimodal",https://research.aimultiple.com/llm-pricing/,"2025.csv, 2024.csv, Provider-Features.csv"
Google Gemini,Yes,$300 GCP credits (12 months),15-30 RPM depending on model,"500-1,500 requests/day",1M+ tokens/day for some models,"Gemini 1.5 Pro, Gemini 1.5 Flash, Gemini 2.0 Flash, Gemini 1.0 Ultra, Gemma models",Google account,Yes (used to improve products),"Free via Gemini Studio + GCP, needs billing account; Basic model only, limited features","https://research.aimultiple.com/llm-pricing/, ai.google.dev","2025.csv, 2024.csv, Provider-Features.csv, grok.csv"
Anthropic Claude,Yes (limited),$5-10 for new users (limited time),"5 RPM, 50 RPD (Tier 1), 5RPM/20TPM/300TPD",Based on credit allocation,No direct free API,"Claude 1, 2, 3 (Opus, Sonnet, Haiku), Claude 3.5 Sonnet, Claude 3.5 Haiku",Account + Credit card (small initial purchase required),Not used for training,"High-quality reasoning and long context (200k+ tokens); Small trial credits, requires payment method setup","https://research.aimultiple.com/llm-pricing/, anthropic.com","2025.csv, 2024.csv, Provider-Features.csv, grok.csv"
Mistral AI,Yes,Free tier (La Plateforme),"1 RPS per model, 500K TPM",1B tokens/month per model,Unlimited UI use; API: limited,"Mistral 7B, Mixtral 8x7B, Mistral Nemo, Mistral Small, Pixtral 12B",Account + Phone verification,Yes (opt-in for free tier),"Open-weight, fast, great for local/hosted LLM use cases; Limited capabilities, training data usage",https://research.aimultiple.com/llm-pricing/,"2025.csv, 2024.csv, Provider-Features.csv"
Cohere,Yes,"1,000 API calls/month",20 RPM,"1,000 requests/month","100–1,000 calls/month","Command R, Command R+, Command-A, Aya models, Aya Expanse 8B, Aya Vision 32B",Account,Not specified,"Optimized for RAG and search-based tasks; Limited but sufficient for testing and prototyping",cohere.com,"2025.csv, 2024.csv, grok.csv"
Meta LLaMA,Yes (via partners),Partner dependent,Partner dependent,Partner dependent,Partner dependent,"LLaMA 2, LLaMA 3, Llama 3.2/3.1/3.3, Llama Vision 11B",Partner dependent,Not specified,Use via platforms like Together.ai, Replicate,N/A,"2025.csv, 2024.csv, grok.csv"
Together.ai,Yes,$1 with payment method / Free access to some models,Up to 60 RPM for free models,100K tokens/day (adjustable),Based on credits,"LLaMA, Mistral, Mixtral, DeepSeek, Llama Vision 11B +",Account + Payment method for credits,Not specified,"Ideal for trying multiple models with one key; Some models completely free, others require credits",together.ai,"2025.csv, 2024.csv, grok.csv"
Perplexity AI,Yes (UI only),$5/month with Pro plan,Based on usage tier,UI-based (free & paid versions),Based on credits,"GPT-4, Claude, Mistral, Perplexity models (Sonar)",Account,Not specified,"UI is free; API access limited; API credits included with Pro subscription only",N/A,"2025.csv, 2024.csv"
Hugging Face,Yes,$0.10/month in credits,"1,000 requests/day (signed users), 20,000 with Pro",Daily limits based on account type,Free tier: 30 compute hours/month,"Falcon, Mistral, LLaMA, various open models",Account,Not specified,"Great for model playgrounds and integration; Serverless inference for models <10GB","https://github.com/cheahjs/free-llm-api-resources, huggingface.co","2025.csv, 2024.csv, Provider-Features.csv, grok.csv"
Replicate,Yes,$10 API usage free/month,N/A,~$10 API usage free/month,~$10 API usage free/month,"SDXL, LLaMA, Mistral, others",Account,Not specified,"Pay-per-use; great for AI agents and LLM APIs; Limited compute, account required","https://www.linkedin.com/posts/raza-abro_ai-llm-opensource-activity-7325827932508078080-gq1w","2025.csv, Provider-Features.csv"
Ollama,Yes (local),No API key needed,Unlimited (local hardware),Unlimited (local hardware),Unlimited (local hardware),"Mistral, LLaMA, Gemma",None,N/A,Requires model download; no API cost,N/A,2025.csv
Groq,Yes,Free tier with daily limits,"6,000-70,000 TPM, 30 RPM","200-14,400 requests/day per model, 14,400 RPD","5,000TPM/500,000 TPD","Mixtral, LLaMA 3, Gemma, DeepSeek R1, Whisper, Mixtral 8x7B Instruct 32k",Account,Not specified,"Extremely fast inference speed; Free tier available, limits may apply","https://github.com/cheahjs/free-llm-api-resources, groq.com","2025.csv, 2024.csv, Provider-Features.csv, grok.csv"
Fireworks.ai,Yes,Free API with open-weight models,N/A,100K tokens/day (subject to change),100K tokens/day (subject to change),"Mixtral, LLaMA, Mistral",Account,Not specified,"Good for experimentation; Trial credits only, account required",https://apidog.com/blog/free-open-source-llm-apis/,"2025.csv, Provider-Features.csv"
OpenRouter,Yes,Free models available,"20 RPM, 50 RPD (1000 with $10 topup), 20 requests/minute",50 requests/day free tier,50 requests/day free tier,"70+ free models including Llama, Mistral, DeepSeek, Gemma, Gemini 2.5 Flash, text-embedding-004",Account,Varies by model provider,"Large selection of free models, shared quota system; Only select models are free, usage limits per model","https://www.linkedin.com/posts/raza-abro_ai-llm-opensource-activity-7325827932508078080-gq1w, openrouter.ai","2024.csv, Provider-Features.csv, grok.csv"
Cerebras,Yes,Free tier with context limits,"30 RPM, 60K TPM per model","14,400 requests/day per model",Free tier limited to 8K context window,"Llama 3.1/3.3, Qwen 3",Account,Not specified,"Free tier limited to 8K context window; Usage limits, sign-up required",https://apidog.com/blog/free-open-source-llm-apis/,"2024.csv, Provider-Features.csv"
GitHub Models,Yes,Free tier based on Copilot subscription,Varies by subscription tier,Extremely restrictive token limits,Usage tied to Copilot subscriptions,"GPT-4o, Claude 3.5, Llama, Phi, Gemini, o1 models, AI21 Jamba 1.5 Large, Llama 3.2 90B Vision, Phi-4",GitHub account,Varies by model,"Access tied to GitHub Copilot subscription level; Usage tied to Copilot subscriptions, limited by plan","https://apidog.com/blog/free-open-source-llm-apis/, github.com","2024.csv, Provider-Features.csv, grok.csv"
Cloudflare Workers AI,Yes,N/A,N/A,10,000 neurons/day,10,000 neurons/day,"DeepSeek R1 Distill Qwen 32B, Llama 3.1 8B Instruct, Mistral 7B Instruct v0.2",Cloudflare account,Not specified,Quota-limited, requires Cloudflare account,"https://apidog.com/blog/free-open-source-llm-apis/, cloudflare.com","Provider-Features.csv, grok.csv"
Quora Poe,Yes,N/A,N/A,Usage caps,Usage caps,Various models,Account,Not specified,"Usage caps, mostly web UI",https://www.linkedin.com/posts/raza-abro_ai-llm-opensource-activity-7325827932508078080-gq1w,Provider-Features.csv
LMSYS Chatbot Arena,Yes,N/A,N/A,Web interface only,Web interface only,Various models,Account,Not specified,"Web interface, not direct API",https://www.linkedin.com/posts/raza-abro_ai-llm-opensource-activity-7325827932508078080-gq1w,Provider-Features.csv
Modal/Baseten,Yes,Credits expire monthly,N/A,N/A,Credits expire monthly,Various models,Account,Not specified,"Credits expire monthly, account required",https://apidog.com/blog/free-open-source-llm-apis/,Provider-Features.csv
