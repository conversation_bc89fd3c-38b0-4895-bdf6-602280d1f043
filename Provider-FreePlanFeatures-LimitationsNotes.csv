"Provider","Free Plan Features","Limitations/Notes"
"OpenAI","https://research.aimultiple.com/llm-pricing/","Usage caps, basic models only"
"Google Gemini","https://research.aimultiple.com/llm-pricing/","Basic model only, limited features"
"Mistral AI","https://research.aimultiple.com/llm-pricing/","Limited capabilities, training data usage"
"Claude.ai","https://research.aimultiple.com/llm-pricing/","Limited usage, basic features only"
"OpenRouter","https://www.linkedin.com/posts/raza-abro_ai-llm-opensource-activity-732582793**********-gq1w","Only select models are free, usage limits per model"
"Groq","https://github.com/cheahjs/free-llm-api-resources","Free tier available, limits may apply"
"Together AI","https://github.com/cheahjs/free-llm-api-resources","Free tier, may require sign-up, usage limits"
"HuggingFace Inference","https://github.com/cheahjs/free-llm-api-resources","Quota-limited, not all models or features available"
"Google AI Studio","https://github.com/cheahjs/free-llm-api-resources","Quota-limited, may require account/payment verification"
"Fireworks AI","https://apidog.com/blog/free-open-source-llm-apis/","Trial credits only, account required"
"Cerebras","https://apidog.com/blog/free-open-source-llm-apis/","Usage limits, sign-up required"
"Cloudflare Workers AI","https://apidog.com/blog/free-open-source-llm-apis/","Quota-limited, requires Cloudflare account"
"Replicate","https://www.linkedin.com/posts/raza-abro_ai-llm-opensource-activity-732582793**********-gq1w","Limited compute, account required"
"GitHub Models","https://apidog.com/blog/free-open-source-llm-apis/","Usage tied to Copilot subscriptions, limited by plan"
"Modal/Baseten","https://apidog.com/blog/free-open-source-llm-apis/","Credits expire monthly, account required"
"Quora Poe","https://www.linkedin.com/posts/raza-abro_ai-llm-opensource-activity-732582793**********-gq1w","Usage caps, mostly web UI"
"LMSYS (Chatbot Arena)","https://www.linkedin.com/posts/raza-abro_ai-llm-opensource-activity-732582793**********-gq1w","Web interface, not direct API"