"Provider","Free Plan Features","Models (Examples)","URL"
"OpenRouter","20 requests/minute, 50 requests/day, 1000 requests/day with $10 lifetime topup","Gemini 2.5 Flash, Gemini 2.0 Flash, Gemma 3 27B Instruct, text-embedding-004","openrouter.ai"
"HuggingFace","$0.10/month in credits, Serverless Inference limited to models <10GB","Various open models","huggingface.co"
"Cohere","20 requests/minute, 1000 requests/month, models share common quota","Command-A, Command-R, Aya Expanse 8B, Aya Vision 32B","cohere.com"
"GitHub Copilot","Dependent on subscription tier (Free/Pro/Pro+/Business/Enterprise)","AI21 Jamba 1.5 Large, Llama 3.2 90B Vision, OpenAI GPT-4o, Phi-4","github.com"
"Cloudflare Workers AI","10,000 neurons/day","DeepSeek R1 Distill Qwen 32B, Llama 3.1 8B Instruct, Mistral 7B Instruct v0.2","cloudflare.com"
"Google","For Gemini 1.5 Flash: 15 RPM, 1 million TPM, 1,500 RPD; For Gemini 1.5 Pro: 2 RPM, 32,000 TPM, 50 RPD","Gemini 1.5 Flash, Gemini 1.5 Pro","ai.google.dev, ai.google.dev"
"Anthropic","5RPM /20TPM/300TPD","Claude 3.5 Sonnet","anthropic.com"
"Groq","30 RPM/14,400 RPD/5,000TPM/500,000 TPD","Mixtral 8x7B Instruct 32k","groq.com"
"Together AI","Free access to Llama Vision 11B + FLUX.1; $5 credit for other models","Llama Vision 11B +, various other models","together.ai"