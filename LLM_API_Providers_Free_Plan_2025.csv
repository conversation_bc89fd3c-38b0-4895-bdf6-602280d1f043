Provider,Free Tier,Usage Limit,Key Models Available,Notes
OpenAI,Free trial credits (for new API users),$5–$18 credit (one-time),"GPT-3.5, GPT-4, GPT-4o","GPT-3.5 is cheaper, GPT-4o is fastest & multimodal."
Anthropic (<PERSON>),"Free tier via platforms like Poe, or limited access via API trial","No direct free API, trial via partners","Claude 1, 2, 3 (Opus, Sonnet, Haiku)",High-quality reasoning and long context (200k+ tokens in Claude 3).
Google AI (Gemini),API free tier available via Google Cloud,$300 GCP credits (12 months),"Gemini 1.5 Pro, Gemini 1.0 Ultra","Free via Gemini Studio + GCP, needs billing account."
Mistral AI,"Free via Le Chat UI, or via HuggingFace & Ollama integrations",Unlimited UI use; API: limited,"Mistral 7B, Mixtral 8x7B","Open-weight, fast, great for local/hosted LLM use cases."
Cohere,Free developer tier via Cohere API,"100–1,000 calls/month","Command R, Command R+",Optimized for RAG and search-based tasks.
Meta (LLaMA),"Open-source models, no API from Meta, but hosted via partners",Partner dependent,"LLaMA 2, LLaMA 3","Use via platforms like Together.ai, Replicate."
Together.ai,Free API access to multiple open models,100K tokens/day (adjustable),"LLaMA, Mistral, Mixtral, DeepSeek",Ideal for trying multiple models with one key.
Perplexity AI,API is not public; offers Claude & GPT-4 access via UI,UI-based (free & paid versions),"GPT-4, Claude, Mistral",UI is free; API access limited.
Hugging Face Inference API,Limited free tier via Spaces or Hosted Inference API,Free tier: 30 compute hours/month,"Falcon, Mistral, LLaMA, more",Great for model playgrounds and integration.
Replicate,Free credit ($10) for inference API,~$10 API usage free/month,"SDXL, LLaMA, Mistral, others",Pay-per-use; great for AI agents and LLM APIs.
Ollama (local),No API key needed – runs models locally,Unlimited (local hardware),"Mistral, LLaMA, Gemma",Requires model download; no API cost.
Groq,API to ultra-fast inference for open models,Free for developers,"Mixtral, LLaMA 3, Gemma",Extremely fast inference speed.
Fireworks.ai,Free API with open-weight models,100K tokens/day (subject to change),"Mixtral, LLaMA, Mistral",Good for experimentation.
