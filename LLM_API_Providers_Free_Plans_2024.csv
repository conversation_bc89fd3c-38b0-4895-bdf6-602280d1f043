﻿Provider,Free_Credits,Rate_Limits,Request_Limits,Key_Models_Available,Data_Usage_Training,Registration_Requirements,Free_Tier_Notes
OpenAI,No ongoing free tier (trial credits vary),"20,000 TPM, 200 RPM",Limited by credits/payment method,"GPT-3.5 Turbo, GPT-4o (limited), GPT-4o mini",Not used for training (paid tier only),Account + Phone number + Payment method for full access,"Free tier discontinued, requires payment method for API access"
Google Gemini API,Free tier available,15-30 RPM depending on model,"500-1,500 requests/day","Gemini 2.0 Flash, Gemini 1.5 Flash, Gemini 1.5 Pro, Gemma models",Yes (used to improve products),Google account,"Generous free tier with 1M+ tokens/day for some models, Google AI Studio completely free"
Anthropic Claude,$5-10 for new users (limited time),"5 RPM, 50 RPD (Tier 1)",Based on credit allocation,"Claude 3.5 Sonnet, Claude 3.5 Haiku",Not used for training,Account + Credit card (small initial purchase required),"Small trial credits, requires payment method setup"
Mistral AI,Free tier (La Plateforme),"1 RPS per model, 500K TPM",1B tokens/month per model,"Mistral 7B, Mistral Nemo, Mistral Small, Pixtral 12B",Yes (opt-in for free tier),Account + Phone verification,Generous free tier but requires data training opt-in
Cohere,"1,000 API calls/month",20 RPM,"1,000 requests/month","Command R, Command R+, Aya models",Not specified,Account,Limited but sufficient for testing and prototyping
Hugging Face,$0.10/month in credits,"1,000 requests/day (signed users), 20,000 with Pro",Daily limits based on account type,"Various open models (Llama, Mistral, etc.), model availability varies",Not specified,Account,"Serverless inference for models <10GB, some exceptions for popular larger models"
OpenRouter,Free models available,"20 RPM, 50 RPD (1000 with $10 topup)",50 requests/day free tier,"70+ free models including Llama, Mistral, DeepSeek, Gemma",Varies by model provider,Account,"Large selection of free models, shared quota system"
Groq,Free tier with daily limits,"6,000-70,000 TPM depending on model","200-14,400 requests/day per model","Llama 3/3.1/3.3, Gemma 2, Mistral, DeepSeek R1, Whisper",Not specified,Account,"Fast inference, good for real-time applications"
Cerebras,Free tier with context limits,"30 RPM, 60K TPM per model","14,400 requests/day per model","Llama 3.1/3.3, Qwen 3",Not specified,Account,Free tier limited to 8K context window
Together AI,$1 with payment method,Up to 60 RPM for free models,Based on credits,"Llama 3.2/3.3, DeepSeek R1 Distill",Not specified,Account + Payment method for credits,"Some models completely free, others require credits"
GitHub Models,Free tier based on Copilot subscription,Varies by subscription tier,Extremely restrictive token limits,"GPT-4o, Claude 3.5, Llama, Phi, Gemini, o1 models",Varies by model,GitHub account,Access tied to GitHub Copilot subscription level
Perplexity API,$5/month with Pro plan,Based on usage tier,Based on credits,Perplexity models (Sonar),Not specified,Account,API credits included with Pro subscription only